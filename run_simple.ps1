#Requires -Version 5.1
<#
.SYNOPSIS
    Simplified JoMaDe application startup script.
.DESCRIPTION
    Starts the JoMaDe application with improved error handling and process management.
.PARAMETER ForceInstallBackendDeps
    Forces reinstallation of backend Python dependencies.
.PARAMETER ForceInstallFrontendDeps
    Forces reinstallation of frontend Node.js dependencies.
#>
[CmdletBinding()]
param (
    [switch]$ForceInstallBackendDeps,
    [switch]$ForceInstallFrontendDeps
)

$ErrorActionPreference = "Continue"
Clear-Host

Write-Host "Starting JoMaDe Application..." -ForegroundColor Cyan

# --- Configuration ---
$projectRoot = $PSScriptRoot
$backendDir = Join-Path $projectRoot "backend"
$frontendDir = Join-Path $projectRoot "frontend"
$venvName = ".venv"
$venvPath = Join-Path $backendDir $venvName
$requirementsFile = Join-Path $backendDir "requirements.txt"
$envFile = Join-Path $projectRoot ".env"

# Global variables for process tracking
$script:backendProcess = $null
$script:frontendProcess = $null
$script:backendPort = 8000
$script:frontendPort = 3000

# --- Helper Functions ---
function Test-PortInUse {
    param ([int]$Port)
    try {
        $tcpConnection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        return $tcpConnection -ne $null
    }
    catch {
        return $false
    }
}

function Stop-ProcessUsingPort {
    param ([int]$Port)
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($connection) {
            $processId = $connection.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "Stopping process '$($process.ProcessName)' (PID: $processId) using port $Port..." -ForegroundColor Yellow
                Stop-Process -Id $processId -Force -ErrorAction Stop
                Write-Host "Process (PID: $processId) stopped." -ForegroundColor Green
                return $true
            }
        }
        return $false
    }
    catch {
        Write-Warning "Error stopping process on port ${Port}: $($_.Exception.Message)"
        return $false
    }
}

function Find-Executable {
    param (
        [string]$CommandName,
        [string[]]$AdditionalPaths = @()
    )
    $executable = Get-Command $CommandName -ErrorAction SilentlyContinue
    if ($executable) {
        return $executable.Source
    }
    return $null
}

function Install-BackendDependencies {
    Write-Host "`n--- Backend Setup ---" -ForegroundColor Magenta

    # Check Python virtual environment
    $venvPythonPath = Join-Path (Join-Path $venvPath "Scripts") "python.exe"

    if (-not (Test-Path $venvPath)) {
        Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
        $pythonExecutable = Find-Executable -CommandName "python"
        if (-not $pythonExecutable) {
            throw "Python executable not found. Please ensure Python 3.7+ is installed and in your PATH."
        }

        Push-Location $backendDir
        try {
            & $pythonExecutable -m venv $venvName
            Write-Host "Virtual environment created." -ForegroundColor Green
        }
        finally {
            Pop-Location
        }
    }

    # Check if dependencies need to be installed
    $needsInstall = $ForceInstallBackendDeps
    if (-not $needsInstall) {
        try {
            $fastApiCheck = & $venvPythonPath -c "import fastapi; print('OK')" 2>$null
            $needsInstall = ($fastApiCheck -ne "OK")
        }
        catch {
            $needsInstall = $true
        }
    }

    if ($needsInstall) {
        Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
        Push-Location $backendDir
        try {
            & $venvPythonPath -m pip install -r $requirementsFile --upgrade --quiet
            Write-Host "Backend dependencies installed successfully." -ForegroundColor Green
        }
        catch {
            throw "Failed to install backend dependencies: $($_.Exception.Message)"
        }
        finally {
            Pop-Location
        }
    } else {
        Write-Host "Backend dependencies are already installed." -ForegroundColor Green
    }

    return $venvPythonPath
}

function Install-FrontendDependencies {
    Write-Host "`n--- Frontend Setup ---" -ForegroundColor Magenta

    $npmPath = Find-Executable -CommandName "npm"
    if (-not $npmPath) {
        throw "npm not found. Please ensure Node.js and npm are installed and in your PATH."
    }

    # Check if dependencies need to be installed
    $needsInstall = $ForceInstallFrontendDeps -or (-not (Test-Path (Join-Path $frontendDir "node_modules")))

    if ($needsInstall) {
        Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
        Push-Location $frontendDir
        try {
            & $npmPath install --legacy-peer-deps --loglevel=warn
            Write-Host "Frontend dependencies installed successfully." -ForegroundColor Green
        }
        catch {
            throw "Failed to install frontend dependencies: $($_.Exception.Message)"
        }
        finally {
            Pop-Location
        }
    } else {
        Write-Host "Frontend dependencies are already installed." -ForegroundColor Green
    }

    return $npmPath
}

function Start-BackendServer {
    param([string]$PythonPath)

    Write-Host "`n--- Starting Backend Server ---" -ForegroundColor Magenta

    # Find available port
    if (Test-PortInUse -Port $script:backendPort) {
        Write-Host "Port $($script:backendPort) is in use, trying to stop existing process..." -ForegroundColor Yellow
        if (-not (Stop-ProcessUsingPort -Port $script:backendPort)) {
            $script:backendPort = 8001
            if (Test-PortInUse -Port $script:backendPort) {
                Stop-ProcessUsingPort -Port $script:backendPort | Out-Null
            }
        }
    }

    # Start backend process
    Write-Host "Starting backend server on port $($script:backendPort)..." -ForegroundColor Yellow
    $env:PYTHONPATH = $backendDir

    $script:backendProcess = Start-Process -FilePath $PythonPath -ArgumentList "-m", "uvicorn", "api.main:app", "--reload", "--host", "0.0.0.0", "--port", "$($script:backendPort)", "--log-level", "info" -WorkingDirectory $backendDir -PassThru -WindowStyle Minimized

    Write-Host "Backend server started (PID: $($script:backendProcess.Id))" -ForegroundColor Green
    Start-Sleep -Seconds 8

    # Health check
    $healthUrl = "http://localhost:$($script:backendPort)/docs"
    for ($i = 1; $i -le 3; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $healthUrl -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Host "Backend server is healthy!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Host "Health check attempt $i failed, retrying..." -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        }
    }

    Write-Warning "Backend server health check failed, but continuing..."
    return $false
}

function Start-FrontendServer {
    param([string]$NpmPath)

    Write-Host "`n--- Starting Frontend Server ---" -ForegroundColor Magenta

    # Find available port
    if (Test-PortInUse -Port $script:frontendPort) {
        Write-Host "Port $($script:frontendPort) is in use, trying to stop existing process..." -ForegroundColor Yellow
        if (-not (Stop-ProcessUsingPort -Port $script:frontendPort)) {
            $script:frontendPort = 3001
            if (Test-PortInUse -Port $script:frontendPort) {
                Stop-ProcessUsingPort -Port $script:frontendPort | Out-Null
            }
        }
    }

    # Start frontend process
    Write-Host "Starting frontend server on port $($script:frontendPort)..." -ForegroundColor Yellow

    $script:frontendProcess = Start-Process -FilePath $NpmPath -ArgumentList "run", "dev", "--", "-p", "$($script:frontendPort)" -WorkingDirectory $frontendDir -PassThru -WindowStyle Minimized

    Write-Host "Frontend server started (PID: $($script:frontendProcess.Id))" -ForegroundColor Green
    Start-Sleep -Seconds 20

    # Health check
    $healthUrl = "http://localhost:$($script:frontendPort)"
    for ($i = 1; $i -le 3; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $healthUrl -UseBasicParsing -TimeoutSec 15 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Host "Frontend server is healthy!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Host "Health check attempt $i failed, retrying..." -ForegroundColor Yellow
            Start-Sleep -Seconds 8
        }
    }

    Write-Warning "Frontend server health check failed, but continuing..."
    return $false
}

function Stop-AllServers {
    Write-Host "`n--- Stopping Servers ---" -ForegroundColor Magenta

    if ($script:frontendProcess -and -not $script:frontendProcess.HasExited) {
        Write-Host "Stopping frontend server..." -ForegroundColor Yellow
        try {
            Stop-Process -Id $script:frontendProcess.Id -Force
            Write-Host "Frontend server stopped." -ForegroundColor Green
        }
        catch {
            Write-Warning "Could not stop frontend server: $($_.Exception.Message)"
        }
    }

    if ($script:backendProcess -and -not $script:backendProcess.HasExited) {
        Write-Host "Stopping backend server..." -ForegroundColor Yellow
        try {
            Stop-Process -Id $script:backendProcess.Id -Force
            Write-Host "Backend server stopped." -ForegroundColor Green
        }
        catch {
            Write-Warning "Could not stop backend server: $($_.Exception.Message)"
        }
    }
}

# --- Main Execution ---
try {
    # Install dependencies
    $pythonPath = Install-BackendDependencies
    $npmPath = Install-FrontendDependencies

    # Start servers
    $backendHealthy = Start-BackendServer -PythonPath $pythonPath
    $frontendHealthy = Start-FrontendServer -NpmPath $npmPath

    # Show status
    Write-Host "`n--- Application Status ---" -ForegroundColor Cyan
    if ($backendHealthy) {
        Write-Host "✓ Backend: http://localhost:$($script:backendPort)/docs" -ForegroundColor Green
    } else {
        Write-Host "⚠ Backend: http://localhost:$($script:backendPort)/docs - may still be starting" -ForegroundColor Yellow
    }

    if ($frontendHealthy) {
        Write-Host "✓ Frontend: http://localhost:$($script:frontendPort)" -ForegroundColor Green
    } else {
        Write-Host "⚠ Frontend: http://localhost:$($script:frontendPort) - may still be starting" -ForegroundColor Yellow
    }

    # Open browser
    if ($frontendHealthy -or $script:frontendProcess) {
        $frontendUrl = "http://localhost:$($script:frontendPort)"
        Write-Host "`nOpening $frontendUrl in browser..." -ForegroundColor Cyan
        try {
            Start-Process $frontendUrl
        }
        catch {
            Write-Host "Please manually navigate to: $frontendUrl" -ForegroundColor Yellow
        }
    }

    Write-Host "`nJoMaDe application is running!" -ForegroundColor Green
    Write-Host "Press Enter to stop all servers..." -ForegroundColor Yellow
    Read-Host
}
catch {
    Write-Error "Error during startup: $($_.Exception.Message)"
}
finally {
    Stop-AllServers
    Write-Host "`nJoMaDe application stopped." -ForegroundColor Cyan
}
