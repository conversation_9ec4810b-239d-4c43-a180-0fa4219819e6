#Requires -Version 5.1
<#
.SYNOPSIS
    Fixed JoMaDe application startup script.
.DESCRIPTION
    Starts the JoMaDe application with minimal complexity and maximum reliability.
#>

param (
    [switch]$ForceInstallBackendDeps,
    [switch]$ForceInstallFrontendDeps
)

$ErrorActionPreference = "Continue"
Clear-Host

Write-Host "=== Starting JoMaDe Application ===" -ForegroundColor Cyan

# Configuration
$projectRoot = $PSScriptRoot
$backendDir = Join-Path $projectRoot "backend"
$frontendDir = Join-Path $projectRoot "frontend"
$venvPath = Join-Path $backendDir ".venv"
$pythonPath = Join-Path $venvPath "Scripts\python.exe"

# Global process variables
$script:backendProcess = $null
$script:frontendProcess = $null
$script:backendPort = 8000
$script:frontendPort = 3000

# Helper functions
function Test-PortInUse {
    param ([int]$Port)
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        return $connection -ne $null
    }
    catch {
        return $false
    }
}

function Stop-ProcessUsingPort {
    param ([int]$Port)
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($connection) {
            $processId = $connection.OwningProcess
            Write-Host "Stopping process (PID: $processId) using port $Port..." -ForegroundColor Yellow
            Stop-Process -Id $processId -Force -ErrorAction Stop
            Start-Sleep -Seconds 2
            return $true
        }
        return $false
    }
    catch {
        Write-Warning "Error stopping process on port ${Port}: $($_.Exception.Message)"
        return $false
    }
}

function Start-BackendServer {
    Write-Host "`n--- Starting Backend Server ---" -ForegroundColor Magenta

    # Check if Python virtual environment exists
    if (-not (Test-Path $pythonPath)) {
        Write-Error "Python virtual environment not found at $pythonPath"
        Write-Host "Please run the setup first or check your Python installation." -ForegroundColor Red
        return $false
    }

    # Find available port
    if (Test-PortInUse -Port $script:backendPort) {
        Write-Host "Port $($script:backendPort) is in use, trying to stop existing process..." -ForegroundColor Yellow
        if (-not (Stop-ProcessUsingPort -Port $script:backendPort)) {
            $script:backendPort = 8001
            Write-Host "Trying port $($script:backendPort) instead..." -ForegroundColor Yellow
            if (Test-PortInUse -Port $script:backendPort) {
                Stop-ProcessUsingPort -Port $script:backendPort | Out-Null
            }
        }
    }

    # Start backend process
    Write-Host "Starting backend server on port $($script:backendPort)..." -ForegroundColor Yellow

    try {
        $env:PYTHONPATH = $backendDir
        $script:backendProcess = Start-Process -FilePath $pythonPath -ArgumentList "-m", "uvicorn", "api.main:app", "--reload", "--host", "0.0.0.0", "--port", "$($script:backendPort)" -WorkingDirectory $backendDir -PassThru -WindowStyle Minimized

        if ($script:backendProcess) {
            Write-Host "Backend server started (PID: $($script:backendProcess.Id))" -ForegroundColor Green
            Start-Sleep -Seconds 8

            # Simple health check
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$($script:backendPort)/docs" -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "Backend server is healthy!" -ForegroundColor Green
                    return $true
                }
            }
            catch {
                Write-Host "Backend health check failed, but server may still be starting..." -ForegroundColor Yellow
            }
            return $true
        }
        else {
            Write-Error "Failed to start backend server"
            return $false
        }
    }
    catch {
        Write-Error "Error starting backend server: $($_.Exception.Message)"
        return $false
    }
}

function Start-FrontendServer {
    Write-Host "`n--- Starting Frontend Server ---" -ForegroundColor Magenta

    # Check if npm is available
    $npmPath = Get-Command "npm" -ErrorAction SilentlyContinue
    if (-not $npmPath) {
        Write-Error "npm not found. Please ensure Node.js is installed."
        return $false
    }

    # Check if node_modules exists
    if (-not (Test-Path (Join-Path $frontendDir "node_modules"))) {
        Write-Host "node_modules not found. Installing dependencies..." -ForegroundColor Yellow
        Push-Location $frontendDir
        try {
            & npm install --legacy-peer-deps --loglevel=warn
            Write-Host "Frontend dependencies installed." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
            return $false
        }
        finally {
            Pop-Location
        }
    }

    # Find available port
    if (Test-PortInUse -Port $script:frontendPort) {
        Write-Host "Port $($script:frontendPort) is in use, trying to stop existing process..." -ForegroundColor Yellow
        if (-not (Stop-ProcessUsingPort -Port $script:frontendPort)) {
            $script:frontendPort = 3001
            Write-Host "Trying port $($script:frontendPort) instead..." -ForegroundColor Yellow
            if (Test-PortInUse -Port $script:frontendPort) {
                Stop-ProcessUsingPort -Port $script:frontendPort | Out-Null
            }
        }
    }

    # Start frontend process
    Write-Host "Starting frontend server on port $($script:frontendPort)..." -ForegroundColor Yellow

    try {
        $script:frontendProcess = Start-Process -FilePath $npmPath.Source -ArgumentList "run", "dev", "--", "-p", "$($script:frontendPort)" -WorkingDirectory $frontendDir -PassThru -WindowStyle Minimized

        if ($script:frontendProcess) {
            Write-Host "Frontend server started (PID: $($script:frontendProcess.Id))" -ForegroundColor Green
            Start-Sleep -Seconds 20

            # Simple health check
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$($script:frontendPort)" -UseBasicParsing -TimeoutSec 15 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "Frontend server is healthy!" -ForegroundColor Green
                    return $true
                }
            }
            catch {
                Write-Host "Frontend health check failed, but server may still be starting..." -ForegroundColor Yellow
            }
            return $true
        }
        else {
            Write-Error "Failed to start frontend server"
            return $false
        }
    }
    catch {
        Write-Error "Error starting frontend server: $($_.Exception.Message)"
        return $false
    }
}

function Stop-AllServers {
    Write-Host "`n--- Stopping Servers ---" -ForegroundColor Magenta

    if ($script:frontendProcess -and -not $script:frontendProcess.HasExited) {
        Write-Host "Stopping frontend server..." -ForegroundColor Yellow
        try {
            Stop-Process -Id $script:frontendProcess.Id -Force
            Write-Host "Frontend server stopped." -ForegroundColor Green
        }
        catch {
            Write-Warning "Could not stop frontend server: $($_.Exception.Message)"
        }
    }

    if ($script:backendProcess -and -not $script:backendProcess.HasExited) {
        Write-Host "Stopping backend server..." -ForegroundColor Yellow
        try {
            Stop-Process -Id $script:backendProcess.Id -Force
            Write-Host "Backend server stopped." -ForegroundColor Green
        }
        catch {
            Write-Warning "Could not stop backend server: $($_.Exception.Message)"
        }
    }
}

# Main execution
try {
    # Start servers
    $backendStarted = Start-BackendServer
    $frontendStarted = Start-FrontendServer

    # Show status
    Write-Host "`n=== Application Status ===" -ForegroundColor Cyan

    if ($backendStarted) {
        Write-Host "[OK] Backend: http://localhost:$($script:backendPort)/docs" -ForegroundColor Green
    } else {
        Write-Host "[FAIL] Backend: Failed to start" -ForegroundColor Red
    }

    if ($frontendStarted) {
        Write-Host "[OK] Frontend: http://localhost:$($script:frontendPort)" -ForegroundColor Green
    } else {
        Write-Host "[FAIL] Frontend: Failed to start" -ForegroundColor Red
    }

    # Open browser if frontend is running
    if ($frontendStarted) {
        $frontendUrl = "http://localhost:$($script:frontendPort)"
        Write-Host "`nOpening $frontendUrl in browser..." -ForegroundColor Cyan
        try {
            Start-Process $frontendUrl
        }
        catch {
            Write-Host "Please manually navigate to: $frontendUrl" -ForegroundColor Yellow
        }
    }

    if ($backendStarted -or $frontendStarted) {
        Write-Host "`nJoMaDe application is running!" -ForegroundColor Green
        Write-Host "Both servers are running in minimized windows." -ForegroundColor DarkGray
        Write-Host "Press Enter to stop all servers..." -ForegroundColor Yellow
        Read-Host
    } else {
        Write-Host "`nFailed to start any servers. Check the error messages above." -ForegroundColor Red
        Write-Host "Press Enter to exit..." -ForegroundColor Yellow
        Read-Host
    }
}
catch {
    Write-Error "Critical error during startup: $($_.Exception.Message)"
}
finally {
    Stop-AllServers
    Write-Host "`nJoMaDe application stopped." -ForegroundColor Cyan
}
